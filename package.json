{"name": "job-platform", "version": "1.0.0", "private": true, "workspaces": ["company-portal", "admin-portal", "shared"], "scripts": {"dev": "concurrently \"yarn workspace backend dev\" \"yarn workspace company-portal dev\" \"yarn workspace admin-portal dev\"", "build": "yarn workspace shared build && yarn workspace backend build && yarn workspace company-portal build && yarn workspace admin-portal build", "start": "concurrently \"yarn workspace backend start\" \"yarn workspace company-portal start\" \"yarn workspace admin-portal start\"", "mobile": "yarn workspace mobile-app start", "test": "yarn workspaces run test", "lint": "yarn workspaces run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\""}, "devDependencies": {"concurrently": "^8.2.0", "prettier": "^3.0.0"}}